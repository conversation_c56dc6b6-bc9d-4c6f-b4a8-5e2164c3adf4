import cv2
import numpy as np

# =========================
# HSV Ranges for Red & Blue
# =========================
lower_red_1 = np.array([0, 150, 150])
upper_red_1 = np.array([10, 255, 255])
lower_red_2 = np.array([170, 150, 150])
upper_red_2 = np.array([180, 255, 255])
lower_blue = np.array([100, 150, 100])
upper_blue = np.array([140, 255, 255])

# ====================
# Webcam Initialization
# ====================
cap = cv2.VideoCapture(0)
cap.set(cv2.CAP_PROP_FRAME_WIDTH, 1280)
cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 720)

# Reference point (center of camera view)
reference_point = (640, 360)

# =======================================
# Function to detect black and white tiles
# =======================================
def detect_field_squares(frame):
    gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
    blurred = cv2.GaussianBlur(gray, (5, 5), 0)

    _, thresh_white = cv2.threshold(blurred, 200, 255, cv2.THRESH_BINARY)
    _, thresh_black = cv2.threshold(blurred, 50, 255, cv2.THRESH_BINARY_INV)

    contours_white, _ = cv2.findContours(thresh_white, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    contours_black, _ = cv2.findContours(thresh_black, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

    for cnt in contours_white + contours_black:
        area = cv2.contourArea(cnt)
        if 1000 < area < 50000:
            x, y, w, h = cv2.boundingRect(cnt)
            cv2.rectangle(frame, (x, y), (x + w, y + h), (0, 255, 0), 2)
            cv2.putText(frame, "Square", (x, y - 5), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 1)

# ====================================================
# Function to get center and bounding box of a contour
# ====================================================
def get_largest_contour_center_and_box(mask):
    contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    if not contours:
        return None, None

    largest = max(contours, key=cv2.contourArea)
    M = cv2.moments(largest)
    if M["m00"] == 0:
        return None, None

    cx = int(M["m10"] / M["m00"])
    cy = int(M["m01"] / M["m00"])
    x, y, w, h = cv2.boundingRect(largest)
    return (cx, cy), (x, y, w, h)

# =========================
# Frame Counter (for timing)
# =========================
frame_count = 0

# ===================
# Main Processing Loop
# ===================
while True:
    ret, frame = cap.read()
    if not ret:
        break
    frame_count += 1

    detect_field_squares(frame)
    hsv = cv2.cvtColor(frame, cv2.COLOR_BGR2HSV)

    mask_red1 = cv2.inRange(hsv, lower_red_1, upper_red_1)
    mask_red2 = cv2.inRange(hsv, lower_red_2, upper_red_2)
    mask_red = cv2.bitwise_or(mask_red1, mask_red2)
    mask_blue = cv2.inRange(hsv, lower_blue, upper_blue)

    kernel = np.ones((5, 5), np.uint8)
    mask_red = cv2.morphologyEx(mask_red, cv2.MORPH_OPEN, kernel)
    mask_blue = cv2.morphologyEx(mask_blue, cv2.MORPH_OPEN, kernel)

    # =====================
    # Detect RED Object
    # =====================
    red_center, red_box = get_largest_contour_center_and_box(mask_red)
    if red_center and red_box:
        cx, cy = red_center
        x, y, w, h = red_box
        dx, dy = cx - reference_point[0], cy - reference_point[1]

        direction = ""
        if abs(dx) > 50:
            direction += "Right" if dx > 0 else "Left"
        if abs(dy) > 50:
            direction += " Down" if dy > 0 else " Up"

        cv2.circle(frame, (cx, cy), 10, (0, 0, 255), -1)
        cv2.rectangle(frame, (x, y), (x + w, y + h), (0, 0, 255), 2)
        cv2.putText(frame, f"Red: ({cx},{cy})", (x, y - 10), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 0, 255), 2)
        cv2.putText(frame, f"{direction.strip()}", (cx + 20, cy), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 0, 255), 2)

        if frame_count % 10 == 0:
            print(f"Red Center: ({cx}, {cy}) | Offset: dx={dx}, dy={dy} | Direction: {direction}")

    # =====================
    # Detect BLUE Object
    # =====================
    blue_center, blue_box = get_largest_contour_center_and_box(mask_blue)
    if blue_center and blue_box:
        cx, cy = blue_center
        x, y, w, h = blue_box
        dx, dy = cx - reference_point[0], cy - reference_point[1]

        direction = ""
        if abs(dx) > 50:
            direction += "Right" if dx > 0 else "Left"
        if abs(dy) > 50:
            direction += " Down" if dy > 0 else " Up"

        cv2.circle(frame, (cx, cy), 10, (255, 0, 0), -1)
        cv2.rectangle(frame, (x, y), (x + w, y + h), (255, 0, 0), 2)
        cv2.putText(frame, f"Blue: ({cx},{cy})", (x, y - 10), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 0, 0), 2)
        cv2.putText(frame, f"{direction.strip()}", (cx + 20, cy), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 0, 0), 2)

        if frame_count % 10 == 0:
            print(f"Blue Center: ({cx}, {cy}) | Offset: dx={dx}, dy={dy} | Direction: {direction}")

    # Show red mask window
    #cv2.imshow("Red Mask", mask_red)

    # Show final annotated result
    cv2.imshow("Color + Field Detection", frame)

    if cv2.waitKey(1) & 0xFF == ord('q'):
        break

# Cleanup
cap.release()
cv2.destroyAllWindows()
