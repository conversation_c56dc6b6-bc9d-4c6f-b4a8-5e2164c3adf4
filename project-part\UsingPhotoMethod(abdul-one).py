import cv2
import numpy as np
import time

# HSV color ranges (improved for better red detection & avoiding skin detection)
lower_red_1 = np.array([0, 120, 70])    # Lower bound for red (first hue range)
upper_red_1 = np.array([10, 255, 255])
lower_red_2 = np.array([160, 120, 70])  # Upper bound for red (second hue range)
upper_red_2 = np.array([180, 255, 255])
lower_blue = np.array([100, 150, 100])  # Blue color lower HSV bound
upper_blue = np.array([140, 255, 255])  # Blue color upper HSV bound

reference_point = (640, 360)  # Reference point: center of 1280x720 frame

# Open webcam and set resolution
cap = cv2.VideoCapture(0)
cap.set(cv2.CAP_PROP_FRAME_WIDTH, 1280)
cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 720)

# Function to generate movement direction based on object's offset from center
def give_direction(dx, dy, tolerance=50):
    direction = []
    if dx < -tolerance:
        direction.append("Move Left")
    elif dx > tolerance:
        direction.append("Move Right")
    if dy < -tolerance:
        direction.append("Move Up")
    elif dy > tolerance:
        direction.append("Move Down")
    return direction if direction else ["Stay Centered"]

# Process a single frame: detect red and blue objects, show visuals & direction
def process_frame(frame):
    hsv = cv2.cvtColor(frame, cv2.COLOR_BGR2HSV)  # Convert frame to HSV color space

    # Create masks for red (merged two red hue ranges)
    mask_red1 = cv2.inRange(hsv, lower_red_1, upper_red_1)
    mask_red2 = cv2.inRange(hsv, lower_red_2, upper_red_2)
    mask_red = cv2.bitwise_or(mask_red1, mask_red2)

    # Create mask for blue
    mask_blue = cv2.inRange(hsv, lower_blue, upper_blue)

    # Apply morphological operations to clean the masks
    kernel = np.ones((5, 5), np.uint8)
    mask_red = cv2.morphologyEx(mask_red, cv2.MORPH_OPEN, kernel)
    mask_blue = cv2.morphologyEx(mask_blue, cv2.MORPH_OPEN, kernel)

    # Function to detect and draw objects of a specific color
    def detect_color(mask, color_name, circle_color, text_color):
        contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        if not contours:
            print(f"❌ No {color_name} objects detected.")
            return

        for contour in contours:
            if cv2.contourArea(contour) < 100:  # Ignore small contours (noise)
                continue

            # Get the center of the contour
            M = cv2.moments(contour)
            if M["m00"] != 0:
                cx = int(M["m10"] / M["m00"])
                cy = int(M["m01"] / M["m00"])
                x, y, w, h = cv2.boundingRect(contour)
                dx, dy = cx - reference_point[0], cy - reference_point[1]

                # Calculate movement directions
                directions = give_direction(dx, dy)

                # Draw detection visuals
                cv2.circle(frame, (cx, cy), 10, circle_color, -1)
                cv2.rectangle(frame, (x, y), (x + w, y + h), circle_color, 2)
                cv2.putText(frame, f"{color_name}: ({cx},{cy})", (x, y - 10),
                            cv2.FONT_HERSHEY_SIMPLEX, 0.6, text_color, 2)

                # Print results with directions
                print(f"{color_name} Center: ({cx}, {cy}) | Offset: dx={dx}, dy={dy} | Direction: {', '.join(directions)}")

    # Detect and annotate red and blue objects
    detect_color(mask_red, "Red", (0, 0, 255), (0, 0, 255))
    detect_color(mask_blue, "Blue", (255, 0, 0), (255, 0, 0))

    # Draw reference point in the center of the frame
    cv2.circle(frame, reference_point, 10, (0, 255, 0), -1)
    cv2.putText(frame, "Center", (reference_point[0] - 50, reference_point[1] - 10),
                cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 0), 2)

    # Display processed frame for 1 second (snapshot style)
    cv2.imshow("Snapshot Detection", frame)q
    cv2.waitKey(1000)  # Show for 1 second
    cv2.destroyAllWindows()

# Main loop: capture a frame every second (1 FPS) and process it
try:
    while True:
        print("\n📸 Capturing snapshot at 1 FPS...")
        ret, frame = cap.read()
        if ret:
            process_frame(frame)
        else:
            print("❌ Failed to capture frame.")

        time.sleep(1)  # Wait 1 second between frames

except KeyboardInterrupt:
    print("🛑 Process interrupted by user.")

# Release camera and close any OpenCV windows
cap.release()
cv2.destroyAllWindows()
